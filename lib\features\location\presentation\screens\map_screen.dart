import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rozana/data/models/adress_model.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_button.dart';
import '../../bloc/location bloc/location_bloc.dart';

class MapScreen extends StatefulWidget {
  final AddressModel? address;
  final Function(double latitude, double longitude)? onLocationSelected;

  const MapScreen({
    super.key,
    this.address,
    this.onLocationSelected,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  GoogleMapController? _mapController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize location using BLoC
    context.read<LocationBloc>().add(
          LocationEvent.loadInitialLocation(
            existingAddress: widget.address,
          ),
        );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onCameraMove(CameraPosition position) {
    // Update coordinates in BLoC as map moves
    context.read<LocationBloc>().add(
          LocationEvent.updateCameraPosition(
            latitude: position.target.latitude,
            longitude: position.target.longitude,
          ),
        );
  }

  void _onCameraIdle() {
    // Fetch address when camera stops moving
    final state = context.read<LocationBloc>().state;
    state.whenOrNull(
      mapState: (latitude, longitude, currentAddress, isLoadingAddress,
          isSearching, isLoadingLocation, searchResults, selectedAddress) {
        context.read<LocationBloc>().add(
              LocationEvent.fetchAddressFromCoordinates(
                latitude: latitude,
                longitude: longitude,
              ),
            );
      },
    );
  }

  void _getCurrentLocation() {
    context.read<LocationBloc>().add(const LocationEvent.getCurrentLocation());
  }

  void _searchAndMoveToLocation(String query) {
    if (query.trim().isEmpty) return;
    context.read<LocationBloc>().add(LocationEvent.searchPlace(query: query));
  }

  void _selectLocation() {
    context.read<LocationBloc>().add(const LocationEvent.confirmLocation());
  }

  void _animateToLocation(double latitude, double longitude) {
    _mapController?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(latitude, longitude),
          zoom: 16,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LocationBloc, LocationState>(
      listener: (context, state) {
        state.whenOrNull(
          mapState: (latitude, longitude, currentAddress, isLoadingAddress,
              isSearching, isLoadingLocation, searchResults, selectedAddress) {
            // Animate camera to new location when coordinates change
            _animateToLocation(latitude, longitude);
          },
          addressSelected:
              (selectedAddress, latitude, longitude, formattedAddress) {
            // Navigate back to AddressFormScreen with selected address
            if (mounted) {
              context.pop({
                'latitude': latitude,
                'longitude': longitude,
                'address': selectedAddress,
              });
            }
          },
          error: (message, isLocationError, isAddressError, isSearchError,
              isSaveError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: Colors.red,
              ),
            );
          },
        );
      },
      builder: (context, state) {
        return state.when(
          initial: () => const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          ),
          loading:
              (isLoadingLocation, isLoadingAddress, isSearching, isSaving) =>
                  const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          ),
          loaded: (address) => const Scaffold(
            body: Center(child: Text('Loaded state - should not be here')),
          ),
          mapState: (latitude,
                  longitude,
                  currentAddress,
                  isLoadingAddress,
                  isSearching,
                  isLoadingLocation,
                  searchResults,
                  selectedAddress) =>
              _buildMapScreen(context, latitude, longitude, currentAddress,
                  isLoadingAddress, isSearching, isLoadingLocation),
          addressSelected:
              (selectedAddress, latitude, longitude, formattedAddress) =>
                  const Scaffold(
            body: Center(child: Text('Address selected')),
          ),
          addressSaved: (savedAddress) => const Scaffold(
            body: Center(child: Text('Address saved')),
          ),
          error: (message, isLocationError, isAddressError, isSearchError,
                  isSaveError) =>
              Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Error: $message'),
                  ElevatedButton(
                    onPressed: () => context.read<LocationBloc>().add(
                          LocationEvent.loadInitialLocation(
                            existingAddress: widget.address,
                          ),
                        ),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMapScreen(
    BuildContext context,
    double latitude,
    double longitude,
    String currentAddress,
    bool isLoadingAddress,
    bool isSearching,
    bool isLoadingLocation,
  ) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Location'),
        backgroundColor: AppColors.primaryAverage,
        foregroundColor: Colors.white,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
        ),
        actions: [
          IconButton(
            onPressed: isLoadingLocation ? null : _getCurrentLocation,
            icon: isLoadingLocation
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.my_location),
            tooltip: 'Get Current Location',
          ),
        ],
      ),
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: LatLng(latitude, longitude),
              zoom: 16,
            ),
            onMapCreated: (controller) {
              _mapController = controller;
            },
            onCameraMove: _onCameraMove,
            onCameraIdle: _onCameraIdle,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: true,
            mapToolbarEnabled: false,
          ),

          // Search bar at the top
          Positioned(
            top: 20,
            left: 16,
            right: 16,
            child: Material(
              elevation: 4,
              borderRadius: BorderRadius.circular(8),
              child: TextField(
                controller: _searchController,
                textInputAction: TextInputAction.search,
                onSubmitted: _searchAndMoveToLocation,
                decoration: InputDecoration(
                  hintText: 'Search for a place or address',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: isSearching
                      ? const Padding(
                          padding: EdgeInsets.all(10),
                          child: SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        )
                      : (_searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                              },
                            )
                          : null),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 14),
                ),
              ),
            ),
          ),

          // Static marker in center
          const Center(
            child: Icon(
              Icons.location_pin,
              size: 40,
              color: AppColors.primaryAverage,
            ),
          ),

          // Address info card
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 34),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Selected Location',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryAverage,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          currentAddress,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      if (isLoadingAddress)
                        const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  CustomButton(
                    text: 'Select This Location',
                    onPressed: _selectLocation,
                    backgroundColor: AppColors.primaryAverage,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
