import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../data/models/adress_model.dart';

part 'location_event.freezed.dart';

@freezed
class LocationEvent with _$LocationEvent {
  // Original events
  const factory LocationEvent.started() = _Started;
  const factory LocationEvent.refreshLocation() = _RefreshLocation;

  // Map-specific events
  const factory LocationEvent.loadInitialLocation({
    AddressModel? existingAddress,
  }) = _LoadInitialLocation;

  const factory LocationEvent.updateCameraPosition({
    required double latitude,
    required double longitude,
  }) = _UpdateCameraPosition;

  const factory LocationEvent.fetchAddressFromCoordinates({
    required double latitude,
    required double longitude,
  }) = _FetchAddressFromCoordinates;

  const factory LocationEvent.searchPlace({
    required String query,
  }) = _SearchPlace;

  const factory LocationEvent.confirmLocation() = _ConfirmLocation;

  const factory LocationEvent.getCurrentLocation() = _GetCurrentLocation;

  // Address form events
  const factory LocationEvent.saveAddress({
    required AddressModel address,
  }) = _SaveAddress;

  const factory LocationEvent.selectAddressFromMap({
    required AddressModel address,
  }) = _SelectAddressFromMap;

  const factory LocationEvent.clearSelectedAddress() = _ClearSelectedAddress;
}
