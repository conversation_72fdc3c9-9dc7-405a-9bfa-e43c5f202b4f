// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LocationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationEventCopyWith<$Res> {
  factory $LocationEventCopyWith(
          LocationEvent value, $Res Function(LocationEvent) then) =
      _$LocationEventCopyWithImpl<$Res, LocationEvent>;
}

/// @nodoc
class _$LocationEventCopyWithImpl<$Res, $Val extends LocationEvent>
    implements $LocationEventCopyWith<$Res> {
  _$LocationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'LocationEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements LocationEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$RefreshLocationImplCopyWith<$Res> {
  factory _$$RefreshLocationImplCopyWith(_$RefreshLocationImpl value,
          $Res Function(_$RefreshLocationImpl) then) =
      __$$RefreshLocationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RefreshLocationImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$RefreshLocationImpl>
    implements _$$RefreshLocationImplCopyWith<$Res> {
  __$$RefreshLocationImplCopyWithImpl(
      _$RefreshLocationImpl _value, $Res Function(_$RefreshLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RefreshLocationImpl implements _RefreshLocation {
  const _$RefreshLocationImpl();

  @override
  String toString() {
    return 'LocationEvent.refreshLocation()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RefreshLocationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return refreshLocation();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return refreshLocation?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (refreshLocation != null) {
      return refreshLocation();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return refreshLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return refreshLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (refreshLocation != null) {
      return refreshLocation(this);
    }
    return orElse();
  }
}

abstract class _RefreshLocation implements LocationEvent {
  const factory _RefreshLocation() = _$RefreshLocationImpl;
}

/// @nodoc
abstract class _$$LoadInitialLocationImplCopyWith<$Res> {
  factory _$$LoadInitialLocationImplCopyWith(_$LoadInitialLocationImpl value,
          $Res Function(_$LoadInitialLocationImpl) then) =
      __$$LoadInitialLocationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel? existingAddress});
}

/// @nodoc
class __$$LoadInitialLocationImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$LoadInitialLocationImpl>
    implements _$$LoadInitialLocationImplCopyWith<$Res> {
  __$$LoadInitialLocationImplCopyWithImpl(_$LoadInitialLocationImpl _value,
      $Res Function(_$LoadInitialLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? existingAddress = freezed,
  }) {
    return _then(_$LoadInitialLocationImpl(
      existingAddress: freezed == existingAddress
          ? _value.existingAddress
          : existingAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
    ));
  }
}

/// @nodoc

class _$LoadInitialLocationImpl implements _LoadInitialLocation {
  const _$LoadInitialLocationImpl({this.existingAddress});

  @override
  final AddressModel? existingAddress;

  @override
  String toString() {
    return 'LocationEvent.loadInitialLocation(existingAddress: $existingAddress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadInitialLocationImpl &&
            (identical(other.existingAddress, existingAddress) ||
                other.existingAddress == existingAddress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, existingAddress);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadInitialLocationImplCopyWith<_$LoadInitialLocationImpl> get copyWith =>
      __$$LoadInitialLocationImplCopyWithImpl<_$LoadInitialLocationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return loadInitialLocation(existingAddress);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return loadInitialLocation?.call(existingAddress);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (loadInitialLocation != null) {
      return loadInitialLocation(existingAddress);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return loadInitialLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return loadInitialLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (loadInitialLocation != null) {
      return loadInitialLocation(this);
    }
    return orElse();
  }
}

abstract class _LoadInitialLocation implements LocationEvent {
  const factory _LoadInitialLocation({final AddressModel? existingAddress}) =
      _$LoadInitialLocationImpl;

  AddressModel? get existingAddress;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadInitialLocationImplCopyWith<_$LoadInitialLocationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateCameraPositionImplCopyWith<$Res> {
  factory _$$UpdateCameraPositionImplCopyWith(_$UpdateCameraPositionImpl value,
          $Res Function(_$UpdateCameraPositionImpl) then) =
      __$$UpdateCameraPositionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({double latitude, double longitude});
}

/// @nodoc
class __$$UpdateCameraPositionImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$UpdateCameraPositionImpl>
    implements _$$UpdateCameraPositionImplCopyWith<$Res> {
  __$$UpdateCameraPositionImplCopyWithImpl(_$UpdateCameraPositionImpl _value,
      $Res Function(_$UpdateCameraPositionImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
  }) {
    return _then(_$UpdateCameraPositionImpl(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$UpdateCameraPositionImpl implements _UpdateCameraPosition {
  const _$UpdateCameraPositionImpl(
      {required this.latitude, required this.longitude});

  @override
  final double latitude;
  @override
  final double longitude;

  @override
  String toString() {
    return 'LocationEvent.updateCameraPosition(latitude: $latitude, longitude: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateCameraPositionImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude));
  }

  @override
  int get hashCode => Object.hash(runtimeType, latitude, longitude);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateCameraPositionImplCopyWith<_$UpdateCameraPositionImpl>
      get copyWith =>
          __$$UpdateCameraPositionImplCopyWithImpl<_$UpdateCameraPositionImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return updateCameraPosition(latitude, longitude);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return updateCameraPosition?.call(latitude, longitude);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (updateCameraPosition != null) {
      return updateCameraPosition(latitude, longitude);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return updateCameraPosition(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return updateCameraPosition?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (updateCameraPosition != null) {
      return updateCameraPosition(this);
    }
    return orElse();
  }
}

abstract class _UpdateCameraPosition implements LocationEvent {
  const factory _UpdateCameraPosition(
      {required final double latitude,
      required final double longitude}) = _$UpdateCameraPositionImpl;

  double get latitude;
  double get longitude;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateCameraPositionImplCopyWith<_$UpdateCameraPositionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FetchAddressFromCoordinatesImplCopyWith<$Res> {
  factory _$$FetchAddressFromCoordinatesImplCopyWith(
          _$FetchAddressFromCoordinatesImpl value,
          $Res Function(_$FetchAddressFromCoordinatesImpl) then) =
      __$$FetchAddressFromCoordinatesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({double latitude, double longitude});
}

/// @nodoc
class __$$FetchAddressFromCoordinatesImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$FetchAddressFromCoordinatesImpl>
    implements _$$FetchAddressFromCoordinatesImplCopyWith<$Res> {
  __$$FetchAddressFromCoordinatesImplCopyWithImpl(
      _$FetchAddressFromCoordinatesImpl _value,
      $Res Function(_$FetchAddressFromCoordinatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
  }) {
    return _then(_$FetchAddressFromCoordinatesImpl(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$FetchAddressFromCoordinatesImpl
    implements _FetchAddressFromCoordinates {
  const _$FetchAddressFromCoordinatesImpl(
      {required this.latitude, required this.longitude});

  @override
  final double latitude;
  @override
  final double longitude;

  @override
  String toString() {
    return 'LocationEvent.fetchAddressFromCoordinates(latitude: $latitude, longitude: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FetchAddressFromCoordinatesImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude));
  }

  @override
  int get hashCode => Object.hash(runtimeType, latitude, longitude);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FetchAddressFromCoordinatesImplCopyWith<_$FetchAddressFromCoordinatesImpl>
      get copyWith => __$$FetchAddressFromCoordinatesImplCopyWithImpl<
          _$FetchAddressFromCoordinatesImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return fetchAddressFromCoordinates(latitude, longitude);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return fetchAddressFromCoordinates?.call(latitude, longitude);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (fetchAddressFromCoordinates != null) {
      return fetchAddressFromCoordinates(latitude, longitude);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return fetchAddressFromCoordinates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return fetchAddressFromCoordinates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (fetchAddressFromCoordinates != null) {
      return fetchAddressFromCoordinates(this);
    }
    return orElse();
  }
}

abstract class _FetchAddressFromCoordinates implements LocationEvent {
  const factory _FetchAddressFromCoordinates(
      {required final double latitude,
      required final double longitude}) = _$FetchAddressFromCoordinatesImpl;

  double get latitude;
  double get longitude;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FetchAddressFromCoordinatesImplCopyWith<_$FetchAddressFromCoordinatesImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchPlaceImplCopyWith<$Res> {
  factory _$$SearchPlaceImplCopyWith(
          _$SearchPlaceImpl value, $Res Function(_$SearchPlaceImpl) then) =
      __$$SearchPlaceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$$SearchPlaceImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$SearchPlaceImpl>
    implements _$$SearchPlaceImplCopyWith<$Res> {
  __$$SearchPlaceImplCopyWithImpl(
      _$SearchPlaceImpl _value, $Res Function(_$SearchPlaceImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
  }) {
    return _then(_$SearchPlaceImpl(
      query: null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchPlaceImpl implements _SearchPlace {
  const _$SearchPlaceImpl({required this.query});

  @override
  final String query;

  @override
  String toString() {
    return 'LocationEvent.searchPlace(query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchPlaceImpl &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchPlaceImplCopyWith<_$SearchPlaceImpl> get copyWith =>
      __$$SearchPlaceImplCopyWithImpl<_$SearchPlaceImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return searchPlace(query);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return searchPlace?.call(query);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (searchPlace != null) {
      return searchPlace(query);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return searchPlace(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return searchPlace?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (searchPlace != null) {
      return searchPlace(this);
    }
    return orElse();
  }
}

abstract class _SearchPlace implements LocationEvent {
  const factory _SearchPlace({required final String query}) = _$SearchPlaceImpl;

  String get query;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchPlaceImplCopyWith<_$SearchPlaceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConfirmLocationImplCopyWith<$Res> {
  factory _$$ConfirmLocationImplCopyWith(_$ConfirmLocationImpl value,
          $Res Function(_$ConfirmLocationImpl) then) =
      __$$ConfirmLocationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConfirmLocationImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$ConfirmLocationImpl>
    implements _$$ConfirmLocationImplCopyWith<$Res> {
  __$$ConfirmLocationImplCopyWithImpl(
      _$ConfirmLocationImpl _value, $Res Function(_$ConfirmLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConfirmLocationImpl implements _ConfirmLocation {
  const _$ConfirmLocationImpl();

  @override
  String toString() {
    return 'LocationEvent.confirmLocation()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ConfirmLocationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return confirmLocation();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return confirmLocation?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (confirmLocation != null) {
      return confirmLocation();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return confirmLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return confirmLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (confirmLocation != null) {
      return confirmLocation(this);
    }
    return orElse();
  }
}

abstract class _ConfirmLocation implements LocationEvent {
  const factory _ConfirmLocation() = _$ConfirmLocationImpl;
}

/// @nodoc
abstract class _$$GetCurrentLocationImplCopyWith<$Res> {
  factory _$$GetCurrentLocationImplCopyWith(_$GetCurrentLocationImpl value,
          $Res Function(_$GetCurrentLocationImpl) then) =
      __$$GetCurrentLocationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GetCurrentLocationImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$GetCurrentLocationImpl>
    implements _$$GetCurrentLocationImplCopyWith<$Res> {
  __$$GetCurrentLocationImplCopyWithImpl(_$GetCurrentLocationImpl _value,
      $Res Function(_$GetCurrentLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GetCurrentLocationImpl implements _GetCurrentLocation {
  const _$GetCurrentLocationImpl();

  @override
  String toString() {
    return 'LocationEvent.getCurrentLocation()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GetCurrentLocationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return getCurrentLocation();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return getCurrentLocation?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (getCurrentLocation != null) {
      return getCurrentLocation();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return getCurrentLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return getCurrentLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (getCurrentLocation != null) {
      return getCurrentLocation(this);
    }
    return orElse();
  }
}

abstract class _GetCurrentLocation implements LocationEvent {
  const factory _GetCurrentLocation() = _$GetCurrentLocationImpl;
}

/// @nodoc
abstract class _$$SaveAddressImplCopyWith<$Res> {
  factory _$$SaveAddressImplCopyWith(
          _$SaveAddressImpl value, $Res Function(_$SaveAddressImpl) then) =
      __$$SaveAddressImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel address});
}

/// @nodoc
class __$$SaveAddressImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$SaveAddressImpl>
    implements _$$SaveAddressImplCopyWith<$Res> {
  __$$SaveAddressImplCopyWithImpl(
      _$SaveAddressImpl _value, $Res Function(_$SaveAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
  }) {
    return _then(_$SaveAddressImpl(
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class _$SaveAddressImpl implements _SaveAddress {
  const _$SaveAddressImpl({required this.address});

  @override
  final AddressModel address;

  @override
  String toString() {
    return 'LocationEvent.saveAddress(address: $address)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SaveAddressImpl &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SaveAddressImplCopyWith<_$SaveAddressImpl> get copyWith =>
      __$$SaveAddressImplCopyWithImpl<_$SaveAddressImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return saveAddress(address);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return saveAddress?.call(address);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (saveAddress != null) {
      return saveAddress(address);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return saveAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return saveAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (saveAddress != null) {
      return saveAddress(this);
    }
    return orElse();
  }
}

abstract class _SaveAddress implements LocationEvent {
  const factory _SaveAddress({required final AddressModel address}) =
      _$SaveAddressImpl;

  AddressModel get address;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SaveAddressImplCopyWith<_$SaveAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectAddressFromMapImplCopyWith<$Res> {
  factory _$$SelectAddressFromMapImplCopyWith(_$SelectAddressFromMapImpl value,
          $Res Function(_$SelectAddressFromMapImpl) then) =
      __$$SelectAddressFromMapImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel address});
}

/// @nodoc
class __$$SelectAddressFromMapImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$SelectAddressFromMapImpl>
    implements _$$SelectAddressFromMapImplCopyWith<$Res> {
  __$$SelectAddressFromMapImplCopyWithImpl(_$SelectAddressFromMapImpl _value,
      $Res Function(_$SelectAddressFromMapImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
  }) {
    return _then(_$SelectAddressFromMapImpl(
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class _$SelectAddressFromMapImpl implements _SelectAddressFromMap {
  const _$SelectAddressFromMapImpl({required this.address});

  @override
  final AddressModel address;

  @override
  String toString() {
    return 'LocationEvent.selectAddressFromMap(address: $address)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectAddressFromMapImpl &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectAddressFromMapImplCopyWith<_$SelectAddressFromMapImpl>
      get copyWith =>
          __$$SelectAddressFromMapImplCopyWithImpl<_$SelectAddressFromMapImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return selectAddressFromMap(address);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return selectAddressFromMap?.call(address);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (selectAddressFromMap != null) {
      return selectAddressFromMap(address);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return selectAddressFromMap(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return selectAddressFromMap?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (selectAddressFromMap != null) {
      return selectAddressFromMap(this);
    }
    return orElse();
  }
}

abstract class _SelectAddressFromMap implements LocationEvent {
  const factory _SelectAddressFromMap({required final AddressModel address}) =
      _$SelectAddressFromMapImpl;

  AddressModel get address;

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelectAddressFromMapImplCopyWith<_$SelectAddressFromMapImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearSelectedAddressImplCopyWith<$Res> {
  factory _$$ClearSelectedAddressImplCopyWith(_$ClearSelectedAddressImpl value,
          $Res Function(_$ClearSelectedAddressImpl) then) =
      __$$ClearSelectedAddressImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearSelectedAddressImplCopyWithImpl<$Res>
    extends _$LocationEventCopyWithImpl<$Res, _$ClearSelectedAddressImpl>
    implements _$$ClearSelectedAddressImplCopyWith<$Res> {
  __$$ClearSelectedAddressImplCopyWithImpl(_$ClearSelectedAddressImpl _value,
      $Res Function(_$ClearSelectedAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ClearSelectedAddressImpl implements _ClearSelectedAddress {
  const _$ClearSelectedAddressImpl();

  @override
  String toString() {
    return 'LocationEvent.clearSelectedAddress()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ClearSelectedAddressImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() refreshLocation,
    required TResult Function(AddressModel? existingAddress)
        loadInitialLocation,
    required TResult Function(double latitude, double longitude)
        updateCameraPosition,
    required TResult Function(double latitude, double longitude)
        fetchAddressFromCoordinates,
    required TResult Function(String query) searchPlace,
    required TResult Function() confirmLocation,
    required TResult Function() getCurrentLocation,
    required TResult Function(AddressModel address) saveAddress,
    required TResult Function(AddressModel address) selectAddressFromMap,
    required TResult Function() clearSelectedAddress,
  }) {
    return clearSelectedAddress();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? refreshLocation,
    TResult? Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult? Function(double latitude, double longitude)? updateCameraPosition,
    TResult? Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult? Function(String query)? searchPlace,
    TResult? Function()? confirmLocation,
    TResult? Function()? getCurrentLocation,
    TResult? Function(AddressModel address)? saveAddress,
    TResult? Function(AddressModel address)? selectAddressFromMap,
    TResult? Function()? clearSelectedAddress,
  }) {
    return clearSelectedAddress?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? refreshLocation,
    TResult Function(AddressModel? existingAddress)? loadInitialLocation,
    TResult Function(double latitude, double longitude)? updateCameraPosition,
    TResult Function(double latitude, double longitude)?
        fetchAddressFromCoordinates,
    TResult Function(String query)? searchPlace,
    TResult Function()? confirmLocation,
    TResult Function()? getCurrentLocation,
    TResult Function(AddressModel address)? saveAddress,
    TResult Function(AddressModel address)? selectAddressFromMap,
    TResult Function()? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (clearSelectedAddress != null) {
      return clearSelectedAddress();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_RefreshLocation value) refreshLocation,
    required TResult Function(_LoadInitialLocation value) loadInitialLocation,
    required TResult Function(_UpdateCameraPosition value) updateCameraPosition,
    required TResult Function(_FetchAddressFromCoordinates value)
        fetchAddressFromCoordinates,
    required TResult Function(_SearchPlace value) searchPlace,
    required TResult Function(_ConfirmLocation value) confirmLocation,
    required TResult Function(_GetCurrentLocation value) getCurrentLocation,
    required TResult Function(_SaveAddress value) saveAddress,
    required TResult Function(_SelectAddressFromMap value) selectAddressFromMap,
    required TResult Function(_ClearSelectedAddress value) clearSelectedAddress,
  }) {
    return clearSelectedAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_RefreshLocation value)? refreshLocation,
    TResult? Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult? Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult? Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult? Function(_SearchPlace value)? searchPlace,
    TResult? Function(_ConfirmLocation value)? confirmLocation,
    TResult? Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult? Function(_SaveAddress value)? saveAddress,
    TResult? Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult? Function(_ClearSelectedAddress value)? clearSelectedAddress,
  }) {
    return clearSelectedAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_RefreshLocation value)? refreshLocation,
    TResult Function(_LoadInitialLocation value)? loadInitialLocation,
    TResult Function(_UpdateCameraPosition value)? updateCameraPosition,
    TResult Function(_FetchAddressFromCoordinates value)?
        fetchAddressFromCoordinates,
    TResult Function(_SearchPlace value)? searchPlace,
    TResult Function(_ConfirmLocation value)? confirmLocation,
    TResult Function(_GetCurrentLocation value)? getCurrentLocation,
    TResult Function(_SaveAddress value)? saveAddress,
    TResult Function(_SelectAddressFromMap value)? selectAddressFromMap,
    TResult Function(_ClearSelectedAddress value)? clearSelectedAddress,
    required TResult orElse(),
  }) {
    if (clearSelectedAddress != null) {
      return clearSelectedAddress(this);
    }
    return orElse();
  }
}

abstract class _ClearSelectedAddress implements LocationEvent {
  const factory _ClearSelectedAddress() = _$ClearSelectedAddressImpl;
}
