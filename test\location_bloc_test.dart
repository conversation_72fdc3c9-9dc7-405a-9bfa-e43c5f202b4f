import 'package:flutter_test/flutter_test.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/features/location/services/adress_services.dart';
import 'package:rozana/features/location/services/locaion_services.dart';
import 'package:rozana/data/models/adress_model.dart';

void main() {
  group('LocationBloc', () {
    late LocationBloc locationBloc;

    setUp(() {
      locationBloc = LocationBloc(
        addressService: AddressService(),
        locationService: LocationService(),
      );
    });

    tearDown(() {
      locationBloc.close();
    });

    test('initial state is correct', () {
      expect(locationBloc.state, const LocationState.initial());
    });

    test('can create LocationEvent.loadInitialLocation', () {
      const event = LocationEvent.loadInitialLocation();
      expect(event, isA<LocationEvent>());
    });

    test('can create LocationEvent.saveAddress', () {
      final address = AddressModel(
        id: 'test-id',
        fullAddress: 'Test Address',
        addressLine1: 'Test Line 1',
        city: 'Test City',
        state: 'Test State',
        pincode: '123456',
        latitude: 25.0,
        longitude: 75.0,
        addressType: 'home',
        isDefault: false,
      );

      final event = LocationEvent.saveAddress(address: address);
      expect(event, isA<LocationEvent>());
    });

    test('can create LocationState.mapState', () {
      const state = LocationState.mapState(
        latitude: 25.0,
        longitude: 75.0,
        currentAddress: 'Test Address',
      );
      expect(state, isA<LocationState>());
    });

    test('can create LocationState.addressSelected', () {
      final address = AddressModel(
        id: 'test-id',
        fullAddress: 'Test Address',
        addressLine1: 'Test Line 1',
        city: 'Test City',
        state: 'Test State',
        pincode: '123456',
        latitude: 25.0,
        longitude: 75.0,
        addressType: 'home',
        isDefault: false,
      );

      final state = LocationState.addressSelected(
        selectedAddress: address,
        latitude: 25.0,
        longitude: 75.0,
        formattedAddress: 'Test Address',
      );
      expect(state, isA<LocationState>());
    });

    test('AddressModel can be created with all properties', () {
      final address = AddressModel(
        id: 'test-id',
        fullAddress: 'Test Full Address',
        addressLine1: 'Test Address Line 1',
        landmark: 'Test Landmark',
        city: 'Test City',
        state: 'Test State',
        pincode: '123456',
        latitude: 25.0,
        longitude: 75.0,
        addressType: 'home',
        isDefault: true,
      );

      expect(address.id, 'test-id');
      expect(address.fullAddress, 'Test Full Address');
      expect(address.addressLine1, 'Test Address Line 1');
      expect(address.landmark, 'Test Landmark');
      expect(address.city, 'Test City');
      expect(address.state, 'Test State');
      expect(address.pincode, '123456');
      expect(address.latitude, 25.0);
      expect(address.longitude, 75.0);
      expect(address.addressType, 'home');
      expect(address.isDefault, true);
    });
  });
}
