export 'location_event.dart';
export 'location_state.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../../../../data/models/adress_model.dart';
import '../../services/adress_services.dart';
import '../../services/locaion_services.dart';
import 'location_event.dart';
import 'location_state.dart';

class LocationBloc extends Bloc<LocationEvent, LocationState> {
  final AddressService addressService;
  final LocationService locationService;

  LocationBloc({
    required this.addressService,
    required this.locationService,
  }) : super(const LocationState.initial()) {
    on<LocationEvent>(
      (event, emit) => event.map(
        started: (_) => _onStarted(emit),
        refreshLocation: (e) => _onRefreshLocation(emit),
        loadInitialLocation: (e) => _onLoadInitialLocation(e, emit),
        updateCameraPosition: (e) => _onUpdateCameraPosition(e, emit),
        fetchAddressFromCoordinates: (e) =>
            _onFetchAddressFromCoordinates(e, emit),
        searchPlace: (e) => _onSearchPlace(e, emit),
        confirmLocation: (e) => _onConfirmLocation(emit),
        getCurrentLocation: (e) => _onGetCurrentLocation(emit),
        saveAddress: (e) => _onSaveAddress(e, emit),
        selectAddressFromMap: (e) => _onSelectAddressFromMap(e, emit),
        clearSelectedAddress: (e) => _onClearSelectedAddress(emit),
      ),
    );
  }

  Future<void> _onStarted(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      final defaultAddress = await addressService.getDefaultAddress();
      if (defaultAddress != null) {
        emit(LocationState.loaded(address: defaultAddress));
      } else {
        add(const LocationEvent.refreshLocation());
      }
    } catch (e) {
      emit(LocationState.error(message: 'Failed to load default location.'));
    }
  }

  Future<void> _onRefreshLocation(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());

    try {
      final savedAddresses = await addressService.getAllAddresses();
      final currentPosition = await locationService.getCurrentPosition();

      if (currentPosition != null) {
        if (savedAddresses.isEmpty) {
          final placemarks = await locationService.getAddressFromCoordinates(
            currentPosition.latitude,
            currentPosition.longitude,
          );
          if (placemarks != null && placemarks.isNotEmpty) {
            final address =
                _createTempAddress(currentPosition, placemarks.first);
            emit(LocationState.loaded(address: address));
            return;
          }
        } else {
          AddressModel? closest;
          double minDist = double.infinity;

          for (final addr in savedAddresses) {
            final dist = Geolocator.distanceBetween(
              currentPosition.latitude,
              currentPosition.longitude,
              (addr.latitude ?? 0.0).toDouble(),
              (addr.longitude ?? 0.0).toDouble(),
            );
            if (dist < minDist) {
              minDist = dist;
              closest = addr;
            }
          }

          if (closest != null) {
            if (!(closest.isDefault ?? false)) {
              await addressService.setDefaultAddress(closest.id ?? '');
            }
            emit(LocationState.loaded(address: closest));
            return;
          }
        }
      }

      final fallback = await addressService.getDefaultAddress();
      if (fallback != null) {
        emit(LocationState.loaded(address: fallback));
      } else {
        emit(const LocationState.error(message: 'No address available.'));
      }
    } catch (e) {
      emit(LocationState.error(message: 'Error while refreshing location.'));
    }
  }

  AddressModel _createTempAddress(Position pos, Placemark pm) {
    final fullAddress = [
      pm.street,
      pm.subLocality,
      pm.locality,
      pm.administrativeArea,
      pm.postalCode
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    final addressLine1 = [
      pm.street,
      pm.subLocality,
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    return AddressModel(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: pm.locality ?? '',
      state: pm.administrativeArea ?? '',
      pincode: pm.postalCode ?? '',
      latitude: pos.latitude,
      longitude: pos.longitude,
      addressType: 'current',
      isDefault: true,
    );
  }

  // New event handlers for map functionality
  Future<void> _onLoadInitialLocation(
    LocationEvent event,
    Emitter<LocationState> emit,
  ) async {
    emit(const LocationState.loading(isLoadingLocation: true));

    try {
      double latitude;
      double longitude;

      // Extract existingAddress from the event
      AddressModel? existingAddress;
      event.whenOrNull(
        loadInitialLocation: (address) => existingAddress = address,
      );

      if (existingAddress != null &&
          existingAddress!.latitude != null &&
          existingAddress!.longitude != null) {
        // Use existing address coordinates
        latitude = (existingAddress!.latitude as double);
        longitude = (existingAddress!.longitude as double);
      } else {
        // Get current location
        final position = await locationService.getCurrentPosition();
        if (position != null) {
          latitude = position.latitude;
          longitude = position.longitude;
        } else {
          // Fallback to Delhi coordinates
          latitude = 28.6139;
          longitude = 77.2090;
        }
      }

      // Fetch address for the coordinates
      final placemarks = await locationService.getAddressFromCoordinates(
        latitude,
        longitude,
      );

      String currentAddress = 'Loading address...';
      if (placemarks != null && placemarks.isNotEmpty) {
        currentAddress = _buildAddressString(placemarks.first);
      }

      emit(LocationState.mapState(
        latitude: latitude,
        longitude: longitude,
        currentAddress: currentAddress,
      ));
    } catch (e) {
      emit(LocationState.error(
        message: 'Failed to load initial location',
        isLocationError: true,
      ));
    }
  }

  Future<void> _onUpdateCameraPosition(
    LocationEvent event,
    Emitter<LocationState> emit,
  ) async {
    final currentState = state;

    // Extract coordinates from the event
    double? latitude, longitude;
    event.whenOrNull(
      updateCameraPosition: (lat, lng) {
        latitude = lat;
        longitude = lng;
      },
    );

    if (latitude != null && longitude != null) {
      currentState.whenOrNull(
        mapState: (lat, lng, currentAddress, isLoadingAddress, isSearching,
            isLoadingLocation, searchResults, selectedAddress) {
          emit(LocationState.mapState(
            latitude: latitude!,
            longitude: longitude!,
            currentAddress: currentAddress,
            isLoadingAddress: isLoadingAddress,
            isSearching: isSearching,
            isLoadingLocation: isLoadingLocation,
            searchResults: searchResults,
            selectedAddress: selectedAddress,
          ));
        },
      );
    }
  }

  Future<void> _onFetchAddressFromCoordinates(
    LocationEvent event,
    Emitter<LocationState> emit,
  ) async {
    final currentState = state;

    // Extract coordinates from the event
    double? latitude, longitude;
    event.whenOrNull(
      fetchAddressFromCoordinates: (lat, lng) {
        latitude = lat;
        longitude = lng;
      },
    );

    if (latitude != null && longitude != null) {
      currentState.whenOrNull(
        mapState: (lat, lng, currentAddress, isLoadingAddress, isSearching,
            isLoadingLocation, searchResults, selectedAddress) {
          emit(LocationState.mapState(
            latitude: lat,
            longitude: lng,
            currentAddress: currentAddress,
            isLoadingAddress: true,
            isSearching: isSearching,
            isLoadingLocation: isLoadingLocation,
            searchResults: searchResults,
            selectedAddress: selectedAddress,
          ));
        },
      );

      try {
        final placemarks = await locationService.getAddressFromCoordinates(
          latitude!,
          longitude!,
        );

        String address = 'Address not found';
        if (placemarks != null && placemarks.isNotEmpty) {
          address = _buildAddressString(placemarks.first);
        }

        emit(LocationState.mapState(
          latitude: latitude!,
          longitude: longitude!,
          currentAddress: address,
          isLoadingAddress: false,
        ));
      } catch (e) {
        emit(LocationState.error(
          message: 'Failed to get address',
          isAddressError: true,
        ));
      }
    }
  }

  Future<void> _onSearchPlace(
    LocationEvent event,
    Emitter<LocationState> emit,
  ) async {
    final currentState = state;

    // Extract query from the event
    String? query;
    event.whenOrNull(
      searchPlace: (q) => query = q,
    );

    if (query != null) {
      currentState.whenOrNull(
        mapState: (lat, lng, currentAddress, isLoadingAddress, isSearching,
            isLoadingLocation, searchResults, selectedAddress) {
          emit(LocationState.mapState(
            latitude: lat,
            longitude: lng,
            currentAddress: currentAddress,
            isLoadingAddress: isLoadingAddress,
            isSearching: true,
            isLoadingLocation: isLoadingLocation,
            searchResults: searchResults,
            selectedAddress: selectedAddress,
          ));
        },
      );

      try {
        final locations = await locationService.searchAddresses(query!);
        if (locations != null && locations.isNotEmpty) {
          final location = locations.first;
          final placemarks =
              await locationService.getPlacemarkFromLocation(location);

          String address = 'Address not found';
          if (placemarks != null && placemarks.isNotEmpty) {
            address = _buildAddressString(placemarks.first);
          }

          emit(LocationState.mapState(
            latitude: location.latitude,
            longitude: location.longitude,
            currentAddress: address,
            searchResults: locations,
            isSearching: false,
          ));
        } else {
          emit(LocationState.error(
            message: 'No results found for "$query"',
            isSearchError: true,
          ));
        }
      } catch (e) {
        emit(LocationState.error(
          message: 'Search failed',
          isSearchError: true,
        ));
      }
    }
  }

  String _buildAddressString(Placemark placemark) {
    final parts = [
      placemark.street,
      placemark.subLocality,
      placemark.locality,
      placemark.administrativeArea,
      placemark.postalCode,
    ];
    return parts.where((part) => part != null && part.isNotEmpty).join(', ');
  }

  Future<void> _onConfirmLocation(Emitter<LocationState> emit) async {
    final currentState = state;

    currentState.whenOrNull(
      mapState: (latitude,
          longitude,
          currentAddress,
          isLoadingAddress,
          isSearching,
          isLoadingLocation,
          searchResults,
          selectedAddress) async {
        try {
          // Get placemark for selected location
          final placemarks = await locationService.getAddressFromCoordinates(
            latitude,
            longitude,
          );

          if (placemarks != null && placemarks.isNotEmpty) {
            final placemark = placemarks.first;
            final addressModel = AddressModel(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              fullAddress: _buildAddressString(placemark),
              addressLine1: placemark.street ?? '',
              city: placemark.locality ?? '',
              state: placemark.administrativeArea ?? '',
              pincode: placemark.postalCode ?? '',
              latitude: latitude,
              longitude: longitude,
              addressType: 'home',
              isDefault: false,
            );

            emit(LocationState.addressSelected(
              selectedAddress: addressModel,
              latitude: latitude,
              longitude: longitude,
              formattedAddress: _buildAddressString(placemark),
            ));
          } else {
            emit(LocationState.error(
              message: 'Failed to get address for selected location',
              isAddressError: true,
            ));
          }
        } catch (e) {
          emit(LocationState.error(
            message: 'Failed to confirm location',
            isAddressError: true,
          ));
        }
      },
    );
  }

  Future<void> _onGetCurrentLocation(Emitter<LocationState> emit) async {
    final currentState = state;

    currentState.whenOrNull(
      mapState: (latitude,
          longitude,
          currentAddress,
          isLoadingAddress,
          isSearching,
          isLoadingLocation,
          searchResults,
          selectedAddress) async {
        emit(LocationState.mapState(
          latitude: latitude,
          longitude: longitude,
          currentAddress: currentAddress,
          isLoadingAddress: isLoadingAddress,
          isSearching: isSearching,
          isLoadingLocation: true,
          searchResults: searchResults,
          selectedAddress: selectedAddress,
        ));

        try {
          final position = await locationService.getCurrentPosition();
          if (position != null) {
            final placemarks = await locationService.getAddressFromCoordinates(
              position.latitude,
              position.longitude,
            );

            String address = 'Address not found';
            if (placemarks != null && placemarks.isNotEmpty) {
              address = _buildAddressString(placemarks.first);
            }

            emit(LocationState.mapState(
              latitude: position.latitude,
              longitude: position.longitude,
              currentAddress: address,
              isLoadingLocation: false,
            ));
          } else {
            emit(LocationState.error(
              message: 'Failed to get current location',
              isLocationError: true,
            ));
          }
        } catch (e) {
          emit(LocationState.error(
            message: 'Failed to get current location',
            isLocationError: true,
          ));
        }
      },
    );
  }

  Future<void> _onSaveAddress(
    LocationEvent event,
    Emitter<LocationState> emit,
  ) async {
    emit(const LocationState.loading(isSaving: true));

    // Extract address from the event
    AddressModel? address;
    event.whenOrNull(
      saveAddress: (addr) => address = addr,
    );

    if (address != null) {
      try {
        await addressService.saveAddress(address!);
        emit(LocationState.addressSaved(savedAddress: address!));
      } catch (e) {
        emit(LocationState.error(
          message: 'Failed to save address',
          isSaveError: true,
        ));
      }
    }
  }

  Future<void> _onSelectAddressFromMap(
    LocationEvent event,
    Emitter<LocationState> emit,
  ) async {
    // Extract address from the event
    AddressModel? address;
    event.whenOrNull(
      selectAddressFromMap: (addr) => address = addr,
    );

    if (address != null) {
      emit(LocationState.addressSelected(
        selectedAddress: address!,
        latitude: (address!.latitude as double?) ?? 0.0,
        longitude: (address!.longitude as double?) ?? 0.0,
        formattedAddress: address!.fullAddress ?? '',
      ));
    }
  }

  Future<void> _onClearSelectedAddress(Emitter<LocationState> emit) async {
    emit(const LocationState.initial());
  }
}
