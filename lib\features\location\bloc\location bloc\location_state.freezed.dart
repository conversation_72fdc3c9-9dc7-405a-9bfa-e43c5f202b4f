// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LocationState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)
        loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)
        mapState,
    required TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)
        addressSelected,
    required TResult Function(AddressModel savedAddress) addressSaved,
    required TResult Function(String message, bool isLocationError,
            bool isAddressError, bool isSearchError, bool isSaveError)
        error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult? Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult? Function(AddressModel savedAddress)? addressSaved,
    TResult? Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult Function(AddressModel savedAddress)? addressSaved,
    TResult Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_AddressSelected value) addressSelected,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_Error value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_AddressSelected value)? addressSelected,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_Error value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_MapState value)? mapState,
    TResult Function(_AddressSelected value)? addressSelected,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationStateCopyWith<$Res> {
  factory $LocationStateCopyWith(
          LocationState value, $Res Function(LocationState) then) =
      _$LocationStateCopyWithImpl<$Res, LocationState>;
}

/// @nodoc
class _$LocationStateCopyWithImpl<$Res, $Val extends LocationState>
    implements $LocationStateCopyWith<$Res> {
  _$LocationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'LocationState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)
        loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)
        mapState,
    required TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)
        addressSelected,
    required TResult Function(AddressModel savedAddress) addressSaved,
    required TResult Function(String message, bool isLocationError,
            bool isAddressError, bool isSearchError, bool isSaveError)
        error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult? Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult? Function(AddressModel savedAddress)? addressSaved,
    TResult? Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult Function(AddressModel savedAddress)? addressSaved,
    TResult Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_AddressSelected value) addressSelected,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_AddressSelected value)? addressSelected,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_MapState value)? mapState,
    TResult Function(_AddressSelected value)? addressSelected,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements LocationState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {bool isLoadingLocation,
      bool isLoadingAddress,
      bool isSearching,
      bool isSaving});
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingLocation = null,
    Object? isLoadingAddress = null,
    Object? isSearching = null,
    Object? isSaving = null,
  }) {
    return _then(_$LoadingImpl(
      isLoadingLocation: null == isLoadingLocation
          ? _value.isLoadingLocation
          : isLoadingLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingAddress: null == isLoadingAddress
          ? _value.isLoadingAddress
          : isLoadingAddress // ignore: cast_nullable_to_non_nullable
              as bool,
      isSearching: null == isSearching
          ? _value.isSearching
          : isSearching // ignore: cast_nullable_to_non_nullable
              as bool,
      isSaving: null == isSaving
          ? _value.isSaving
          : isSaving // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl(
      {this.isLoadingLocation = false,
      this.isLoadingAddress = false,
      this.isSearching = false,
      this.isSaving = false});

  @override
  @JsonKey()
  final bool isLoadingLocation;
  @override
  @JsonKey()
  final bool isLoadingAddress;
  @override
  @JsonKey()
  final bool isSearching;
  @override
  @JsonKey()
  final bool isSaving;

  @override
  String toString() {
    return 'LocationState.loading(isLoadingLocation: $isLoadingLocation, isLoadingAddress: $isLoadingAddress, isSearching: $isSearching, isSaving: $isSaving)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadingImpl &&
            (identical(other.isLoadingLocation, isLoadingLocation) ||
                other.isLoadingLocation == isLoadingLocation) &&
            (identical(other.isLoadingAddress, isLoadingAddress) ||
                other.isLoadingAddress == isLoadingAddress) &&
            (identical(other.isSearching, isSearching) ||
                other.isSearching == isSearching) &&
            (identical(other.isSaving, isSaving) ||
                other.isSaving == isSaving));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, isLoadingLocation, isLoadingAddress, isSearching, isSaving);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadingImplCopyWith<_$LoadingImpl> get copyWith =>
      __$$LoadingImplCopyWithImpl<_$LoadingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)
        loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)
        mapState,
    required TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)
        addressSelected,
    required TResult Function(AddressModel savedAddress) addressSaved,
    required TResult Function(String message, bool isLocationError,
            bool isAddressError, bool isSearchError, bool isSaveError)
        error,
  }) {
    return loading(isLoadingLocation, isLoadingAddress, isSearching, isSaving);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult? Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult? Function(AddressModel savedAddress)? addressSaved,
    TResult? Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
  }) {
    return loading?.call(
        isLoadingLocation, isLoadingAddress, isSearching, isSaving);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult Function(AddressModel savedAddress)? addressSaved,
    TResult Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(
          isLoadingLocation, isLoadingAddress, isSearching, isSaving);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_AddressSelected value) addressSelected,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_Error value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_AddressSelected value)? addressSelected,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_Error value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_MapState value)? mapState,
    TResult Function(_AddressSelected value)? addressSelected,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements LocationState {
  const factory _Loading(
      {final bool isLoadingLocation,
      final bool isLoadingAddress,
      final bool isSearching,
      final bool isSaving}) = _$LoadingImpl;

  bool get isLoadingLocation;
  bool get isLoadingAddress;
  bool get isSearching;
  bool get isSaving;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadingImplCopyWith<_$LoadingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel address});
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
  }) {
    return _then(_$LoadedImpl(
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl({required this.address});

  @override
  final AddressModel address;

  @override
  String toString() {
    return 'LocationState.loaded(address: $address)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)
        loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)
        mapState,
    required TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)
        addressSelected,
    required TResult Function(AddressModel savedAddress) addressSaved,
    required TResult Function(String message, bool isLocationError,
            bool isAddressError, bool isSearchError, bool isSaveError)
        error,
  }) {
    return loaded(address);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult? Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult? Function(AddressModel savedAddress)? addressSaved,
    TResult? Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
  }) {
    return loaded?.call(address);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult Function(AddressModel savedAddress)? addressSaved,
    TResult Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(address);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_AddressSelected value) addressSelected,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_Error value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_AddressSelected value)? addressSelected,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_Error value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_MapState value)? mapState,
    TResult Function(_AddressSelected value)? addressSelected,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements LocationState {
  const factory _Loaded({required final AddressModel address}) = _$LoadedImpl;

  AddressModel get address;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MapStateImplCopyWith<$Res> {
  factory _$$MapStateImplCopyWith(
          _$MapStateImpl value, $Res Function(_$MapStateImpl) then) =
      __$$MapStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {double latitude,
      double longitude,
      String currentAddress,
      bool isLoadingAddress,
      bool isSearching,
      bool isLoadingLocation,
      List<Location>? searchResults,
      AddressModel? selectedAddress});
}

/// @nodoc
class __$$MapStateImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$MapStateImpl>
    implements _$$MapStateImplCopyWith<$Res> {
  __$$MapStateImplCopyWithImpl(
      _$MapStateImpl _value, $Res Function(_$MapStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latitude = null,
    Object? longitude = null,
    Object? currentAddress = null,
    Object? isLoadingAddress = null,
    Object? isSearching = null,
    Object? isLoadingLocation = null,
    Object? searchResults = freezed,
    Object? selectedAddress = freezed,
  }) {
    return _then(_$MapStateImpl(
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      currentAddress: null == currentAddress
          ? _value.currentAddress
          : currentAddress // ignore: cast_nullable_to_non_nullable
              as String,
      isLoadingAddress: null == isLoadingAddress
          ? _value.isLoadingAddress
          : isLoadingAddress // ignore: cast_nullable_to_non_nullable
              as bool,
      isSearching: null == isSearching
          ? _value.isSearching
          : isSearching // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingLocation: null == isLoadingLocation
          ? _value.isLoadingLocation
          : isLoadingLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      searchResults: freezed == searchResults
          ? _value._searchResults
          : searchResults // ignore: cast_nullable_to_non_nullable
              as List<Location>?,
      selectedAddress: freezed == selectedAddress
          ? _value.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel?,
    ));
  }
}

/// @nodoc

class _$MapStateImpl implements _MapState {
  const _$MapStateImpl(
      {required this.latitude,
      required this.longitude,
      required this.currentAddress,
      this.isLoadingAddress = false,
      this.isSearching = false,
      this.isLoadingLocation = false,
      final List<Location>? searchResults,
      this.selectedAddress})
      : _searchResults = searchResults;

  @override
  final double latitude;
  @override
  final double longitude;
  @override
  final String currentAddress;
  @override
  @JsonKey()
  final bool isLoadingAddress;
  @override
  @JsonKey()
  final bool isSearching;
  @override
  @JsonKey()
  final bool isLoadingLocation;
  final List<Location>? _searchResults;
  @override
  List<Location>? get searchResults {
    final value = _searchResults;
    if (value == null) return null;
    if (_searchResults is EqualUnmodifiableListView) return _searchResults;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AddressModel? selectedAddress;

  @override
  String toString() {
    return 'LocationState.mapState(latitude: $latitude, longitude: $longitude, currentAddress: $currentAddress, isLoadingAddress: $isLoadingAddress, isSearching: $isSearching, isLoadingLocation: $isLoadingLocation, searchResults: $searchResults, selectedAddress: $selectedAddress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MapStateImpl &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.currentAddress, currentAddress) ||
                other.currentAddress == currentAddress) &&
            (identical(other.isLoadingAddress, isLoadingAddress) ||
                other.isLoadingAddress == isLoadingAddress) &&
            (identical(other.isSearching, isSearching) ||
                other.isSearching == isSearching) &&
            (identical(other.isLoadingLocation, isLoadingLocation) ||
                other.isLoadingLocation == isLoadingLocation) &&
            const DeepCollectionEquality()
                .equals(other._searchResults, _searchResults) &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      latitude,
      longitude,
      currentAddress,
      isLoadingAddress,
      isSearching,
      isLoadingLocation,
      const DeepCollectionEquality().hash(_searchResults),
      selectedAddress);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MapStateImplCopyWith<_$MapStateImpl> get copyWith =>
      __$$MapStateImplCopyWithImpl<_$MapStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)
        loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)
        mapState,
    required TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)
        addressSelected,
    required TResult Function(AddressModel savedAddress) addressSaved,
    required TResult Function(String message, bool isLocationError,
            bool isAddressError, bool isSearchError, bool isSaveError)
        error,
  }) {
    return mapState(latitude, longitude, currentAddress, isLoadingAddress,
        isSearching, isLoadingLocation, searchResults, selectedAddress);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult? Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult? Function(AddressModel savedAddress)? addressSaved,
    TResult? Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
  }) {
    return mapState?.call(latitude, longitude, currentAddress, isLoadingAddress,
        isSearching, isLoadingLocation, searchResults, selectedAddress);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult Function(AddressModel savedAddress)? addressSaved,
    TResult Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
    required TResult orElse(),
  }) {
    if (mapState != null) {
      return mapState(latitude, longitude, currentAddress, isLoadingAddress,
          isSearching, isLoadingLocation, searchResults, selectedAddress);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_AddressSelected value) addressSelected,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_Error value) error,
  }) {
    return mapState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_AddressSelected value)? addressSelected,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_Error value)? error,
  }) {
    return mapState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_MapState value)? mapState,
    TResult Function(_AddressSelected value)? addressSelected,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (mapState != null) {
      return mapState(this);
    }
    return orElse();
  }
}

abstract class _MapState implements LocationState {
  const factory _MapState(
      {required final double latitude,
      required final double longitude,
      required final String currentAddress,
      final bool isLoadingAddress,
      final bool isSearching,
      final bool isLoadingLocation,
      final List<Location>? searchResults,
      final AddressModel? selectedAddress}) = _$MapStateImpl;

  double get latitude;
  double get longitude;
  String get currentAddress;
  bool get isLoadingAddress;
  bool get isSearching;
  bool get isLoadingLocation;
  List<Location>? get searchResults;
  AddressModel? get selectedAddress;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MapStateImplCopyWith<_$MapStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddressSelectedImplCopyWith<$Res> {
  factory _$$AddressSelectedImplCopyWith(_$AddressSelectedImpl value,
          $Res Function(_$AddressSelectedImpl) then) =
      __$$AddressSelectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {AddressModel selectedAddress,
      double latitude,
      double longitude,
      String formattedAddress});
}

/// @nodoc
class __$$AddressSelectedImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$AddressSelectedImpl>
    implements _$$AddressSelectedImplCopyWith<$Res> {
  __$$AddressSelectedImplCopyWithImpl(
      _$AddressSelectedImpl _value, $Res Function(_$AddressSelectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedAddress = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? formattedAddress = null,
  }) {
    return _then(_$AddressSelectedImpl(
      selectedAddress: null == selectedAddress
          ? _value.selectedAddress
          : selectedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      formattedAddress: null == formattedAddress
          ? _value.formattedAddress
          : formattedAddress // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AddressSelectedImpl implements _AddressSelected {
  const _$AddressSelectedImpl(
      {required this.selectedAddress,
      required this.latitude,
      required this.longitude,
      required this.formattedAddress});

  @override
  final AddressModel selectedAddress;
  @override
  final double latitude;
  @override
  final double longitude;
  @override
  final String formattedAddress;

  @override
  String toString() {
    return 'LocationState.addressSelected(selectedAddress: $selectedAddress, latitude: $latitude, longitude: $longitude, formattedAddress: $formattedAddress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddressSelectedImpl &&
            (identical(other.selectedAddress, selectedAddress) ||
                other.selectedAddress == selectedAddress) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.formattedAddress, formattedAddress) ||
                other.formattedAddress == formattedAddress));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, selectedAddress, latitude, longitude, formattedAddress);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddressSelectedImplCopyWith<_$AddressSelectedImpl> get copyWith =>
      __$$AddressSelectedImplCopyWithImpl<_$AddressSelectedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)
        loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)
        mapState,
    required TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)
        addressSelected,
    required TResult Function(AddressModel savedAddress) addressSaved,
    required TResult Function(String message, bool isLocationError,
            bool isAddressError, bool isSearchError, bool isSaveError)
        error,
  }) {
    return addressSelected(
        selectedAddress, latitude, longitude, formattedAddress);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult? Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult? Function(AddressModel savedAddress)? addressSaved,
    TResult? Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
  }) {
    return addressSelected?.call(
        selectedAddress, latitude, longitude, formattedAddress);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult Function(AddressModel savedAddress)? addressSaved,
    TResult Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
    required TResult orElse(),
  }) {
    if (addressSelected != null) {
      return addressSelected(
          selectedAddress, latitude, longitude, formattedAddress);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_AddressSelected value) addressSelected,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_Error value) error,
  }) {
    return addressSelected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_AddressSelected value)? addressSelected,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_Error value)? error,
  }) {
    return addressSelected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_MapState value)? mapState,
    TResult Function(_AddressSelected value)? addressSelected,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (addressSelected != null) {
      return addressSelected(this);
    }
    return orElse();
  }
}

abstract class _AddressSelected implements LocationState {
  const factory _AddressSelected(
      {required final AddressModel selectedAddress,
      required final double latitude,
      required final double longitude,
      required final String formattedAddress}) = _$AddressSelectedImpl;

  AddressModel get selectedAddress;
  double get latitude;
  double get longitude;
  String get formattedAddress;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddressSelectedImplCopyWith<_$AddressSelectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddressSavedImplCopyWith<$Res> {
  factory _$$AddressSavedImplCopyWith(
          _$AddressSavedImpl value, $Res Function(_$AddressSavedImpl) then) =
      __$$AddressSavedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel savedAddress});
}

/// @nodoc
class __$$AddressSavedImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$AddressSavedImpl>
    implements _$$AddressSavedImplCopyWith<$Res> {
  __$$AddressSavedImplCopyWithImpl(
      _$AddressSavedImpl _value, $Res Function(_$AddressSavedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? savedAddress = null,
  }) {
    return _then(_$AddressSavedImpl(
      savedAddress: null == savedAddress
          ? _value.savedAddress
          : savedAddress // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class _$AddressSavedImpl implements _AddressSaved {
  const _$AddressSavedImpl({required this.savedAddress});

  @override
  final AddressModel savedAddress;

  @override
  String toString() {
    return 'LocationState.addressSaved(savedAddress: $savedAddress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddressSavedImpl &&
            (identical(other.savedAddress, savedAddress) ||
                other.savedAddress == savedAddress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, savedAddress);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddressSavedImplCopyWith<_$AddressSavedImpl> get copyWith =>
      __$$AddressSavedImplCopyWithImpl<_$AddressSavedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)
        loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)
        mapState,
    required TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)
        addressSelected,
    required TResult Function(AddressModel savedAddress) addressSaved,
    required TResult Function(String message, bool isLocationError,
            bool isAddressError, bool isSearchError, bool isSaveError)
        error,
  }) {
    return addressSaved(savedAddress);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult? Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult? Function(AddressModel savedAddress)? addressSaved,
    TResult? Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
  }) {
    return addressSaved?.call(savedAddress);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult Function(AddressModel savedAddress)? addressSaved,
    TResult Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
    required TResult orElse(),
  }) {
    if (addressSaved != null) {
      return addressSaved(savedAddress);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_AddressSelected value) addressSelected,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_Error value) error,
  }) {
    return addressSaved(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_AddressSelected value)? addressSelected,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_Error value)? error,
  }) {
    return addressSaved?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_MapState value)? mapState,
    TResult Function(_AddressSelected value)? addressSelected,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (addressSaved != null) {
      return addressSaved(this);
    }
    return orElse();
  }
}

abstract class _AddressSaved implements LocationState {
  const factory _AddressSaved({required final AddressModel savedAddress}) =
      _$AddressSavedImpl;

  AddressModel get savedAddress;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddressSavedImplCopyWith<_$AddressSavedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String message,
      bool isLocationError,
      bool isAddressError,
      bool isSearchError,
      bool isSaveError});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$LocationStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? isLocationError = null,
    Object? isAddressError = null,
    Object? isSearchError = null,
    Object? isSaveError = null,
  }) {
    return _then(_$ErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      isLocationError: null == isLocationError
          ? _value.isLocationError
          : isLocationError // ignore: cast_nullable_to_non_nullable
              as bool,
      isAddressError: null == isAddressError
          ? _value.isAddressError
          : isAddressError // ignore: cast_nullable_to_non_nullable
              as bool,
      isSearchError: null == isSearchError
          ? _value.isSearchError
          : isSearchError // ignore: cast_nullable_to_non_nullable
              as bool,
      isSaveError: null == isSaveError
          ? _value.isSaveError
          : isSaveError // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(
      {required this.message,
      this.isLocationError = false,
      this.isAddressError = false,
      this.isSearchError = false,
      this.isSaveError = false});

  @override
  final String message;
  @override
  @JsonKey()
  final bool isLocationError;
  @override
  @JsonKey()
  final bool isAddressError;
  @override
  @JsonKey()
  final bool isSearchError;
  @override
  @JsonKey()
  final bool isSaveError;

  @override
  String toString() {
    return 'LocationState.error(message: $message, isLocationError: $isLocationError, isAddressError: $isAddressError, isSearchError: $isSearchError, isSaveError: $isSaveError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.isLocationError, isLocationError) ||
                other.isLocationError == isLocationError) &&
            (identical(other.isAddressError, isAddressError) ||
                other.isAddressError == isAddressError) &&
            (identical(other.isSearchError, isSearchError) ||
                other.isSearchError == isSearchError) &&
            (identical(other.isSaveError, isSaveError) ||
                other.isSaveError == isSaveError));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, isLocationError,
      isAddressError, isSearchError, isSaveError);

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)
        loading,
    required TResult Function(AddressModel address) loaded,
    required TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)
        mapState,
    required TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)
        addressSelected,
    required TResult Function(AddressModel savedAddress) addressSaved,
    required TResult Function(String message, bool isLocationError,
            bool isAddressError, bool isSearchError, bool isSaveError)
        error,
  }) {
    return error(
        message, isLocationError, isAddressError, isSearchError, isSaveError);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult? Function(AddressModel address)? loaded,
    TResult? Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult? Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult? Function(AddressModel savedAddress)? addressSaved,
    TResult? Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
  }) {
    return error?.call(
        message, isLocationError, isAddressError, isSearchError, isSaveError);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(bool isLoadingLocation, bool isLoadingAddress,
            bool isSearching, bool isSaving)?
        loading,
    TResult Function(AddressModel address)? loaded,
    TResult Function(
            double latitude,
            double longitude,
            String currentAddress,
            bool isLoadingAddress,
            bool isSearching,
            bool isLoadingLocation,
            List<Location>? searchResults,
            AddressModel? selectedAddress)?
        mapState,
    TResult Function(AddressModel selectedAddress, double latitude,
            double longitude, String formattedAddress)?
        addressSelected,
    TResult Function(AddressModel savedAddress)? addressSaved,
    TResult Function(String message, bool isLocationError, bool isAddressError,
            bool isSearchError, bool isSaveError)?
        error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(
          message, isLocationError, isAddressError, isSearchError, isSaveError);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_MapState value) mapState,
    required TResult Function(_AddressSelected value) addressSelected,
    required TResult Function(_AddressSaved value) addressSaved,
    required TResult Function(_Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_MapState value)? mapState,
    TResult? Function(_AddressSelected value)? addressSelected,
    TResult? Function(_AddressSaved value)? addressSaved,
    TResult? Function(_Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_MapState value)? mapState,
    TResult Function(_AddressSelected value)? addressSelected,
    TResult Function(_AddressSaved value)? addressSaved,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements LocationState {
  const factory _Error(
      {required final String message,
      final bool isLocationError,
      final bool isAddressError,
      final bool isSearchError,
      final bool isSaveError}) = _$ErrorImpl;

  String get message;
  bool get isLocationError;
  bool get isAddressError;
  bool get isSearchError;
  bool get isSaveError;

  /// Create a copy of LocationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
