import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:geocoding/geocoding.dart';
import '../../../../data/models/adress_model.dart';

part 'location_state.freezed.dart';

@freezed
class LocationState with _$LocationState {
  const factory LocationState.initial() = _Initial;

  const factory LocationState.loading({
    @Default(false) bool isLoadingLocation,
    @Default(false) bool isLoadingAddress,
    @Default(false) bool isSearching,
    @Default(false) bool isSaving,
  }) = _Loading;

  const factory LocationState.loaded({
    required AddressModel address,
  }) = _Loaded;

  const factory LocationState.mapState({
    required double latitude,
    required double longitude,
    required String currentAddress,
    @Default(false) bool isLoadingAddress,
    @Default(false) bool isSearching,
    @Default(false) bool isLoadingLocation,
    List<Location>? searchResults,
    AddressModel? selectedAddress,
  }) = _MapState;

  const factory LocationState.addressSelected({
    required AddressModel selectedAddress,
    required double latitude,
    required double longitude,
    required String formattedAddress,
  }) = _AddressSelected;

  const factory LocationState.addressSaved({
    required AddressModel savedAddress,
  }) = _AddressSaved;

  const factory LocationState.error({
    required String message,
    @Default(false) bool isLocationError,
    @Default(false) bool isAddressError,
    @Default(false) bool isSearchError,
    @Default(false) bool isSaveError,
  }) = _Error;
}
